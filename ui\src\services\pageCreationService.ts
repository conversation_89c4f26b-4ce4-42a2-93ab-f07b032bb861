/**
 * Page Creation Service
 * 
 * Centralized service for handling all page creation scenarios with consistent
 * naming, error handling, and logging.
 */

import { generateCleanPageName } from './pageNameService';
import type { 
  PageCreationRequest, 
  PageCreationResult, 
  PageCreationSource,
  PageCreationMetrics 
} from '../types/pageCreation';

/**
 * Generates a unique page ID from a page name
 */
export function generatePageId(pageName: string): string {
  return pageName
    .toLowerCase()
    .replace(/[^a-z0-9\s]/g, '')
    .replace(/\s+/g, '-')
    .replace(/^-+|-+$/g, '');
}

/**
 * Creates a page with clean name generation and proper error handling
 */
export async function createPageWithCleanName(
  request: PageCreationRequest,
  source: PageCreationSource
): Promise<PageCreationResult> {
  const startTime = Date.now();
  
  try {
    // Generate clean page name
    const nameStartTime = Date.now();
    const nameResult = await generateCleanPageName(request.prompt);
    const nameGenerationTime = Date.now() - nameStartTime;
    
    if (!nameResult.success) {
      console.warn(`Page name generation failed for source ${source}:`, nameResult.error);
    }
    
    const pageName = request.pageName || nameResult.pageName;
    const pageId = generatePageId(pageName);
    
    // Log metrics
    const metrics: PageCreationMetrics = {
      source,
      promptLength: request.prompt.length,
      nameGenerationTime,
      nameGenerationSource: nameResult.source,
      totalCreationTime: Date.now() - startTime
    };
    
    logPageCreationMetrics(metrics);
    
    return {
      success: true,
      pageId,
      pageName
    };
    
  } catch (error) {
    console.error(`Page creation failed for source ${source}:`, error);
    
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * Generates a comprehensive prompt for page content generation
 */
export function generatePageContentPrompt(pageName: string, existingPages: any[] = []): string {
  const otherPageNames = existingPages
    .filter(p => p.name !== pageName)
    .map(p => p.name)
    .slice(0, 5); // Limit to avoid overly long prompts

  return `Create a complete, professional ${pageName} page.

🎯 **PAGE REQUIREMENTS:**
- Professional, modern design
- Responsive layout using Tailwind CSS
- Complete HTML structure with proper semantics
- Accessible and user-friendly interface

${otherPageNames.length > 0 ? `
🔗 **NAVIGATION CONTEXT:**
This page is part of a multi-page application. Other pages include: ${otherPageNames.join(', ')}.
Include appropriate navigation elements that reference these pages.
` : ''}

⚡ **INTERACTIVE ELEMENTS:**
- Create buttons and forms that can be enhanced with modal functionality
- Any unimplemented interactive elements will show ⚡ indicators
- Ensure professional design ready for future enhancements
- Include proper structure for modal overlays

🎨 **DESIGN GUIDELINES:**
- Use a clean, professional color scheme
- Implement proper spacing and typography
- Ensure mobile responsiveness
- Include hover states and transitions
- Follow modern web design best practices

Generate a complete, production-ready HTML page.`;
}

/**
 * Logs page creation metrics for monitoring and optimization
 */
function logPageCreationMetrics(metrics: PageCreationMetrics): void {
  console.log('📊 Page Creation Metrics:', {
    source: metrics.source,
    promptLength: metrics.promptLength,
    nameGenerationTime: `${metrics.nameGenerationTime}ms`,
    nameSource: metrics.nameGenerationSource,
    totalTime: `${metrics.totalCreationTime}ms`
  });
  
  // In production, you might want to send these metrics to an analytics service
  // analytics.track('page_created', metrics);
}

/**
 * Validates page creation request
 */
export function validatePageCreationRequest(request: PageCreationRequest): { valid: boolean; error?: string } {
  if (!request.prompt?.trim()) {
    return { valid: false, error: 'Prompt is required' };
  }
  
  if (request.prompt.length > 5000) {
    return { valid: false, error: 'Prompt is too long (max 5000 characters)' };
  }
  
  if (request.pageName && request.pageName.length > 100) {
    return { valid: false, error: 'Page name is too long (max 100 characters)' };
  }
  
  return { valid: true };
}
